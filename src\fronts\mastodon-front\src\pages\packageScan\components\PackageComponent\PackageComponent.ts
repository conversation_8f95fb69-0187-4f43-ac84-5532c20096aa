import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired, FetchError, genericFetch} from "@groupk/horizon2-core";
import {BarcodeExternalNative_QRCodeReaderReturn, VibratorNative} from "@groupk/native-bridge";
import PackageValidationComponent from "../PackageValidationComponent/PackageValidationComponent.vue";
import PackageFormComponent from "../PackageFormComponent/PackageFormComponent.vue";
import {BarcodeScannerManager} from "../../BarcodeScannerManager";
import ItemActionsComponent, {ItemAction} from "../ItemActionsComponent/ItemActionsComponent.vue";
import CameraComponent from "../CameraComponent/CameraComponent.vue";
import ImageGalleryComponent from "../ImageGalleryComponent/ImageGalleryComponent.vue";
import ObjectFormComponent from "../ObjectFormComponent/ObjectFormComponent.vue";
import {BaserowObjectRepository} from "../../../../../../../shared/repositories/BaserowObjectRepository";
import {PackageApiOut} from "../../../../../../../shared/baserowContracts/Package/PackageApiClasses";
import {ObjectNamesApiOut} from "../../../../../../../shared/baserowContracts/ObjectNames/ObjectNamesApiClasses";
import {ObjectApiIn, ObjectApiOut} from "../../../../../../../shared/baserowContracts/Objects/ObjectsApiClasses";

@Component({
    components: {
        'package-validation': PackageValidationComponent,
        'package-form': PackageFormComponent,
        'item-actions': ItemActionsComponent,
        'camera': CameraComponent,
        'image-gallery': ImageGalleryComponent,
        'object-form': ObjectFormComponent
    },
    emits: ['close']
})
export default class PackageComponent extends Vue {
    @Prop({required: true}) packageData!: PackageApiOut;
    @Prop({required: true}) objectTranslations!: ObjectNamesApiOut[];

    objectsInPackage: ObjectApiOut[] = [];

    deleting: boolean = false;
    scanning: boolean = false;
    showValidation: boolean = false;
    showForm: boolean = false;
    showObjectForm: ObjectApiOut|null = null;
    showActionsForObject: ObjectApiOut|null = null;
    showCamera: { opened: boolean, forObject: ObjectApiOut }|null = null;
    showObjectImage: ObjectApiOut|null = null;
    showObjectReturnImage: ObjectApiOut|null = null;
    alreadyIn: ObjectApiOut|null = null;
    loading: boolean = true;

    @AutoWired(BarcodeScannerManager) accessor barcodeScannerManager!: BarcodeScannerManager;
    @AutoWired(BaserowObjectRepository) accessor baserowObjectRepository!: BaserowObjectRepository;
    @AutoWired(VibratorNative) accessor vibratorNative!: VibratorNative;

    async mounted() {
        const response = await this.baserowObjectRepository.callContract('list', undefined, undefined);
        if(!response.isSuccess()) {
            console.log(response);
        } else {
            this.objectsInPackage = response.success().results.filter((object: ObjectApiOut) => {
                return object.packageId[0] && object.packageId[0].id === this.packageData.id
            });
        }

        this.setupListener();

        this.loading = false;
    }

    setupListener() {
        this.barcodeScannerManager.customCallback = this.barCodeRead;
    }

    async barCodeRead(data: BarcodeExternalNative_QRCodeReaderReturn) {
        if(this.scanning) return;

        this.showActionsForObject = null;

        if(this.alreadyIn) {
            this.vibratorNative.vibrate(1000).catch(() => {});
            return;
        }

        this.scanning = true;
        await this.addObjectToPackage(data.content, this.packageData.id);
    }

    async addObjectToPackage(objectCode: string, packageId: number) {

        if(this.isAlreadyIn(objectCode)) {
            this.alreadyIn = this.isAlreadyIn(objectCode);
            this.scanning = false;
            return;
        }


        const response = await this.baserowObjectRepository.callContract('create', undefined, new ObjectApiIn({
            code: objectCode,
            packageId: [packageId],
            returned: false,
            images: undefined,
            returnImages: undefined,
            comment: ''
        }));

        if(!response.isSuccess()) {
        } else {
            this.objectsInPackage.push(response.success());
        }

        this.scanning = false;
    }

    async deleteObjectFromPackage(id: number) {
        this.deleting = true;

        const response = await this.baserowObjectRepository.callContract('delete', {objectId: id}, undefined);
        if(!response.isSuccess()) {
            console.log(response);
        } else {
            console.log('deleting id', id);
            this.objectsInPackage.map((indd)=> console.log(indd.id));
            const index = this.objectsInPackage.findIndex((object) => object.id === id);
            if(index !== -1) {
                this.objectsInPackage.splice(index, 1);
            }
        }

        this.alreadyIn = null;
        this.deleting = false;
    }

    async actionClicked(action: ItemAction) {
        if(!this.showActionsForObject) return;
        if(action.id === 'delete') {
            this.scanning = true;
            await this.deleteObjectFromPackage(this.showActionsForObject.id);
            this.scanning = false;
        } else if(action.id === 'take-picture') {
            this.showCamera = {opened: true, forObject: this.showActionsForObject};
        } else if(action.id === 'show-image') {
            this.showObjectImage = this.showActionsForObject;
        } else if(action.id === 'show-return-image') {
            this.showObjectReturnImage = this.showActionsForObject;
        } else if(action.id === 'edit') {
            this.showObjectForm = this.showActionsForObject;
        }
    }

    async photoCaptured(dataUrl: string) {
        if(!this.showCamera) return;
        this.scanning = true;

        // Convert dataUrl to File object
        const response = await fetch(dataUrl);
        const blob = await response.blob();
        const file = new File([blob], 'captured-image.jpg', { type: blob.type });

        const formData = new FormData()
        formData.append('file', file);

        const responseFile = await genericFetch({
            method: "POST",
            url: "https://baserow-api.lab.weecop.fr/api/user-files/upload-file/",
            headers: {
                Authorization: "Token fCK4Uh9UE8bo8KKRM8dHPvReYiQuf7wL",
            },
            body: formData
        });

        if(responseFile instanceof FetchError) {
        } else {
            const data = await responseFile.json();
            const responseObject = await this.baserowObjectRepository.callContract('update', {objectId: this.showCamera.forObject.id}, new ObjectApiIn({
                code: this.showCamera.forObject.code,
                packageId: undefined,
                returned: this.showCamera.forObject.returned,
                images: [data.name].concat(this.showCamera.forObject.images.map((image) => image.name)),
                returnImages: undefined,
                comment: this.showCamera.forObject.comment ?? ''
            }));

            if(responseObject.isSuccess()) {
                this.updateObject(responseObject.success());
            }
        }

        this.showCamera = null;
        this.scanning = false;
    }

    updatePackage(packageData: PackageApiOut, closeThen: boolean = false) {
        this.packageData.comment = packageData.comment;
        this.packageData.name = packageData.name;
        this.packageData.shipping = packageData.shipping;
        this.packageData.returnCode = packageData.returnCode;
        this.$emit('updated-package', packageData);

        if(closeThen) this.close();
    }

    updateObject(objectData: ObjectApiOut) {
        const index = this.objectsInPackage.findIndex((object) => object.id === objectData.id);
        if(index !== -1) {
            this.objectsInPackage.splice(index, 1, objectData);
        }
    }

    isAlreadyIn(objectCode: string): ObjectApiOut|null {
        return this.objectsInPackage.find((object) => object.code === objectCode) ?? null;
    }

    getObjectTranslation(objectId: string) {
        const translationObject = this.objectTranslations.find((object) => object.code === objectId);
        if(translationObject) {
            if(translationObject.brand) {
                return translationObject.brand + ' ' + translationObject.name + ' - ' + objectId;
            } else {
                return translationObject.name + ' - ' + objectId;
            }
        } else {
            return objectId;
        }
    }

    getObjectActions(objectData: ObjectApiOut) {
        const actions: ItemAction[] = [
            {id: 'take-picture', icon: 'fa-regular fa-camera', text: 'Ajouter une image'},
            {id: 'edit', icon: 'fa-regular fa-pen-line', text: 'Modifier'},
            {id: 'delete', icon: 'fa-regular fa-trash-alt', text: 'Supprimer'}
        ];

        if(objectData.returnImages.length > 0) {
            actions.unshift({id: 'show-return-image', icon: 'fa-regular fa-image', text: 'Visualiser les images de retour'});
        }

        if(objectData.images.length > 0) {
            actions.unshift({id: 'show-image', icon: 'fa-regular fa-image', text: 'Visualiser les images'});
        }

        return actions;
    }

    close() {
        this.$emit('close');
    }
}