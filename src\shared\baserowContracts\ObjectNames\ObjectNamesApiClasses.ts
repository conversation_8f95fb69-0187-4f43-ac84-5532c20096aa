import {
    Entity,
    EntityClass,
    EntityAsSimpleObject,
    IntegerField,
    StringField,
    EntityField
} from "@groupk/horizon2-core";

/**
 * Brand options for the brand field in ObjectNames
 * These correspond to the single select options in Baserow
 */
export const BRAND_OPTIONS = Object.freeze({
    SUNMI: 7485,
    JONKUU: 7486,
    QUILIVE: 7487,
    PAX: 7488
} as const);

export const BRAND_LABELS = {
    [BRAND_OPTIONS.SUNMI]: 'Sunmi',
    [BRAND_OPTIONS.JONKUU]: 'Jonkuu',
    [BRAND_OPTIONS.QUILIVE]: 'Quilive',
    [BRAND_OPTIONS.PAX]: 'PAX'
} as const;

export type BrandOption = typeof BRAND_OPTIONS[keyof typeof BRAND_OPTIONS];

/**
 * ObjectNames API Input class for creating/updating object names in Baserow
 * Based on ObjectNamesEntity structure for Baserow Table ID: 1733
 */
@EntityClass()
export class ObjectNamesApiIn extends Entity {
    /** field_16710: Object code */
    @StringField() code: string;

    /** field_16709: Object name */
    @StringField() name: string;

    /** field_16711: Brand selection (integer or string) */
    @IntegerField() brand: number;

    constructor({
        code,
        name,
        brand
    }: EntityAsSimpleObject<ObjectNamesApiIn>) {
        super();
        this.code = code;
        this.name = name;
        this.brand = brand;
    }
}

/**
 * ObjectNames API Output class for responses from Baserow
 * Based on ObjectNamesEntity structure for Baserow Table ID: 1733
 */
@EntityClass()
export class ObjectNamesApiOut extends Entity {
    @IntegerField() id: number;

    /** field_16710: Object code */
    @StringField() code: string;

    /** field_16709: Object name */
    @StringField() name: string;

    /** field_16711: Brand selection (integer or string) */
    @IntegerField() brand: number;

    constructor({
        id,
        code,
        name,
        brand
    }: EntityAsSimpleObject<ObjectNamesApiOut>) {
        super();
        this.id = id;
        this.code = code;
        this.name = name;
        this.brand = brand;
    }

    /**
     * Get the brand label for the current brand value
     */
    getBrandLabel(): string {
        return BRAND_LABELS[this.brand as BrandOption] || 'Unknown';
    }
}

/**
 * Baserow list response wrapper for ObjectNames entities
 */
@EntityClass()
export class ObjectNamesListApiOut extends Entity {
    @IntegerField() count: number;
    @StringField({nullable: true}) next: string | null;
    @StringField({nullable: true}) previous: string | null;
    @EntityField(ObjectNamesApiOut, {array: true}) results: ObjectNamesApiOut[];

    constructor({
        count,
        next,
        previous,
        results
    }: EntityAsSimpleObject<ObjectNamesListApiOut>) {
        super();
        this.count = count;
        this.next = next;
        this.previous = previous;
        this.results = results;
    }
}
